/**
 * Bug Report Generation
 * 
 * Generates pre-filled GitHub issue URLs with session data,
 * system information, and reproduction steps.
 */

import { platform, arch, release, version } from 'os';
import { CLI_VERSION } from '../version.js';
import type { ResponseItem, ResponseOutputItem } from '../types/index.js';

export interface BugReportData {
  items: Array<ResponseItem | ResponseOutputItem>;
  cliVersion: string;
  model: string;
  provider: string;
  platform: string;
  nodeVersion: string;
  error?: Error;
  additionalContext?: string;
}

/**
 * Build GitHub issue URL with bug report data
 */
export function buildBugReportUrl(data: BugReportData): string {
  const {
    items,
    cliVersion,
    model,
    provider,
    platform: platformInfo,
    nodeVersion,
    error,
    additionalContext,
  } = data;

  // Generate issue title
  const title = error 
    ? `Bug: ${error.message.slice(0, 50)}...`
    : 'Bug Report from Kritrima AI CLI';

  // Generate issue body
  const body = generateIssueBody({
    items,
    cliVersion,
    model,
    provider,
    platform: platformInfo,
    nodeVersion,
    error,
    additionalContext,
  });

  // Encode for URL
  const encodedTitle = encodeURIComponent(title);
  const encodedBody = encodeURIComponent(body);

  // GitHub repository URL (update with actual repository)
  const repoUrl = 'https://github.com/kritrima/kritrima-ai-cli';
  
  return `${repoUrl}/issues/new?title=${encodedTitle}&body=${encodedBody}&labels=bug`;
}

/**
 * Generate issue body content
 */
function generateIssueBody(data: BugReportData): string {
  const {
    items,
    cliVersion,
    model,
    provider,
    platform: platformInfo,
    nodeVersion,
    error,
    additionalContext,
  } = data;

  const sections: string[] = [];

  // Bug description
  sections.push('## Bug Description');
  if (error) {
    sections.push(`**Error:** ${error.message}`);
    sections.push('');
    if (error.stack) {
      sections.push('**Stack Trace:**');
      sections.push('```');
      sections.push(error.stack);
      sections.push('```');
      sections.push('');
    }
  } else {
    sections.push('<!-- Please describe the bug you encountered -->');
    sections.push('');
  }

  // System information
  sections.push('## System Information');
  sections.push(`- **CLI Version:** ${cliVersion}`);
  sections.push(`- **Node.js Version:** ${nodeVersion}`);
  sections.push(`- **Platform:** ${platformInfo}`);
  sections.push(`- **AI Provider:** ${provider}`);
  sections.push(`- **AI Model:** ${model}`);
  sections.push('');

  // Reproduction steps
  sections.push('## Reproduction Steps');
  if (items.length > 0) {
    sections.push('**Session Summary:**');
    const sessionSummary = generateSessionSummary(items);
    sections.push(sessionSummary);
  } else {
    sections.push('<!-- Please describe the steps to reproduce the bug -->');
    sections.push('1. ');
    sections.push('2. ');
    sections.push('3. ');
  }
  sections.push('');

  // Expected vs actual behavior
  sections.push('## Expected Behavior');
  sections.push('<!-- What did you expect to happen? -->');
  sections.push('');
  
  sections.push('## Actual Behavior');
  sections.push('<!-- What actually happened? -->');
  sections.push('');

  // Additional context
  if (additionalContext) {
    sections.push('## Additional Context');
    sections.push(additionalContext);
    sections.push('');
  }

  // Session data (if available)
  if (items.length > 0) {
    sections.push('## Session Data');
    sections.push('<details>');
    sections.push('<summary>Click to expand session details</summary>');
    sections.push('');
    sections.push('```json');
    sections.push(JSON.stringify(sanitizeSessionData(items), null, 2));
    sections.push('```');
    sections.push('</details>');
    sections.push('');
  }

  return sections.join('\n');
}

/**
 * Generate session summary for reproduction steps
 */
function generateSessionSummary(items: Array<ResponseItem | ResponseOutputItem>): string {
  const steps: string[] = [];
  let stepNumber = 1;

  for (const item of items.slice(-10)) { // Last 10 items
    if (item.type === 'message') {
      const content = item.content[0];
      if (content?.type === 'input_text' && content.text) {
        const text = content.text.slice(0, 100);
        steps.push(`${stepNumber}. User input: "${text}${content.text.length > 100 ? '...' : ''}"`);
        stepNumber++;
      } else if (content?.type === 'output_text' && content.text) {
        const text = content.text.slice(0, 100);
        steps.push(`${stepNumber}. AI response: "${text}${content.text.length > 100 ? '...' : ''}"`);
        stepNumber++;
      }
    } else if (item.type === 'function_call') {
      steps.push(`${stepNumber}. Function call executed`);
      stepNumber++;
    } else if (item.type === 'error') {
      steps.push(`${stepNumber}. Error occurred`);
      stepNumber++;
    }
  }

  return steps.join('\n');
}

/**
 * Sanitize session data for public sharing
 */
function sanitizeSessionData(items: Array<ResponseItem | ResponseOutputItem>): any[] {
  return items.map(item => {
    const sanitized: any = {
      type: item.type,
      timestamp: item.timestamp,
    };

    // Sanitize content
    if (item.content) {
      sanitized.content = item.content.map(content => {
        const sanitizedContent: any = {
          type: content.type,
        };

        // Remove potentially sensitive data
        if (content.type === 'input_text' || content.type === 'output_text') {
          const text = content.text || '';
          // Remove API keys, tokens, passwords, etc.
          sanitizedContent.text = sanitizeText(text);
        }

        return sanitizedContent;
      });
    }

    return sanitized;
  });
}

/**
 * Sanitize text content to remove sensitive information
 */
function sanitizeText(text: string): string {
  // Patterns for sensitive data
  const sensitivePatterns = [
    /\b[A-Za-z0-9]{32,}\b/g,           // API keys (32+ chars)
    /\bsk-[A-Za-z0-9]{32,}\b/g,        // OpenAI API keys
    /\bghp_[A-Za-z0-9]{36}\b/g,        // GitHub tokens
    /\bglpat-[A-Za-z0-9_-]{20,}\b/g,   // GitLab tokens
    /password[=:]\s*[^\s]+/gi,         // Passwords
    /token[=:]\s*[^\s]+/gi,            // Tokens
    /secret[=:]\s*[^\s]+/gi,           // Secrets
    /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g, // Credit card numbers
  ];

  let sanitized = text;
  
  for (const pattern of sensitivePatterns) {
    sanitized = sanitized.replace(pattern, '[REDACTED]');
  }

  return sanitized;
}

/**
 * Get system information for bug reports
 */
export function getSystemInfo(): {
  platform: string;
  nodeVersion: string;
  cliVersion: string;
  } {
  return {
    platform: `${platform()} ${arch()} ${release()}`,
    nodeVersion: version(),
    cliVersion: CLI_VERSION,
  };
}

/**
 * Create bug report data from current session
 */
export function createBugReportData(
  items: Array<ResponseItem | ResponseOutputItem>,
  model: string,
  provider: string,
  error?: Error,
  additionalContext?: string,
): BugReportData {
  const systemInfo = getSystemInfo();
  
  return {
    items,
    model,
    provider,
    error,
    additionalContext,
    ...systemInfo,
  };
}

/**
 * Generate quick bug report URL
 */
export function generateQuickBugReport(
  error: Error,
  context?: string,
): string {
  const systemInfo = getSystemInfo();
  
  const data: BugReportData = {
    items: [],
    model: 'unknown',
    provider: 'unknown',
    error,
    additionalContext: context,
    ...systemInfo,
  };
  
  return buildBugReportUrl(data);
}
