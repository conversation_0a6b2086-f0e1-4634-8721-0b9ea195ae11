/**
 * Model Management System
 * 
 * Handles dynamic model discovery, validation, and management
 * across different AI providers with caching and optimization.
 */

import { createOpenAIClient } from './openai-client.js';
import { getProvider, getProviderModels } from './providers.js';
import type { ProviderConfig } from '../types/index.js';

// Model cache to avoid repeated API calls
const modelCache = new Map<string, { models: string[]; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Fetch available models from a provider with caching
 */
export async function fetchModels(provider: string): Promise<string[]> {
  const cacheKey = provider.toLowerCase();
  const cached = modelCache.get(cacheKey);

  // Return cached models if still valid
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.models;
  }

  try {
    // Try to fetch from provider API
    const client = createOpenAIClient({ provider });
    const response = await client.models.list();
    
    const models = response.data
      .map(model => model.id)
      .filter(id => id && typeof id === 'string')
      .sort();

    // Cache the results
    modelCache.set(cacheKey, {
      models,
      timestamp: Date.now(),
    });

    return models;
  } catch (error) {
    console.warn(`Failed to fetch models from ${provider}:`, error);
    
    // Fallback to static model list
    const staticModels = getProviderModels(provider);
    if (staticModels.length > 0) {
      return staticModels;
    }

    // Return empty array if no fallback available
    return [];
  }
}

/**
 * Get models with background refresh
 */
export async function getModelsWithRefresh(provider: string): Promise<string[]> {
  // Start background refresh
  fetchModels(provider).catch(() => {
    // Ignore errors in background refresh
  });

  // Return cached models immediately if available
  const cacheKey = provider.toLowerCase();
  const cached = modelCache.get(cacheKey);
  
  if (cached) {
    return cached.models;
  }

  // If no cache, wait for the fetch
  return fetchModels(provider);
}

/**
 * Validate if a model is available for a provider
 */
export async function validateModel(provider: string, model: string): Promise<boolean> {
  try {
    const availableModels = await fetchModels(provider);
    return availableModels.includes(model);
  } catch (error) {
    console.warn(`Failed to validate model ${model} for provider ${provider}:`, error);
    
    // Fallback to static validation
    const staticModels = getProviderModels(provider);
    return staticModels.includes(model);
  }
}

/**
 * Get model information including context length and capabilities
 */
export function getModelInfo(provider: string, model: string): {
  contextLength: number;
  supportsImages: boolean;
  supportsTools: boolean;
  isChat: boolean;
  isCompletion: boolean;
} {
  const providerConfig = getProvider(provider);
  
  // Default capabilities
  let info = {
    contextLength: providerConfig?.maxContextLength || 4096,
    supportsImages: providerConfig?.supportsImages || false,
    supportsTools: providerConfig?.supportsTools || false,
    isChat: true,
    isCompletion: false,
  };

  // Model-specific overrides
  switch (provider.toLowerCase()) {
  case 'openai':
    info = getOpenAIModelInfo(model);
    break;
  case 'azure':
    info = getAzureModelInfo(model);
    break;
  case 'gemini':
    info = getGeminiModelInfo(model);
    break;
  case 'ollama':
    info = getOllamaModelInfo(model);
    break;
  case 'mistral':
    info = getMistralModelInfo(model);
    break;
  case 'groq':
    info = getGroqModelInfo(model);
    break;
  default:
    // Use provider defaults
    break;
  }

  return info;
}

/**
 * OpenAI model information
 */
function getOpenAIModelInfo(model: string) {
  const baseInfo = {
    contextLength: 4096,
    supportsImages: false,
    supportsTools: true,
    isChat: true,
    isCompletion: false,
  };

  if (model.includes('gpt-4')) {
    baseInfo.contextLength = model.includes('turbo') ? 128000 : 8192;
    baseInfo.supportsImages = model.includes('vision') || model.includes('turbo');
  } else if (model.includes('gpt-3.5-turbo')) {
    baseInfo.contextLength = model.includes('16k') ? 16384 : 4096;
  } else if (model.startsWith('o1')) {
    baseInfo.contextLength = 128000;
    baseInfo.supportsTools = false; // o1 models don't support tools yet
  }

  return baseInfo;
}

/**
 * Azure OpenAI model information
 */
function getAzureModelInfo(model: string) {
  // Azure models follow similar patterns to OpenAI
  return getOpenAIModelInfo(model);
}

/**
 * Google Gemini model information
 */
function getGeminiModelInfo(model: string) {
  const baseInfo = {
    contextLength: 32768,
    supportsImages: false,
    supportsTools: true,
    isChat: true,
    isCompletion: false,
  };

  if (model.includes('1.5')) {
    baseInfo.contextLength = 1000000;
  }

  if (model.includes('vision') || model.includes('pro')) {
    baseInfo.supportsImages = true;
  }

  return baseInfo;
}

/**
 * Ollama model information
 */
function getOllamaModelInfo(model: string) {
  const baseInfo = {
    contextLength: 4096,
    supportsImages: false,
    supportsTools: true,
    isChat: true,
    isCompletion: false,
  };

  // Larger models typically have larger context
  if (model.includes('70b') || model.includes('34b')) {
    baseInfo.contextLength = 8192;
  } else if (model.includes('13b')) {
    baseInfo.contextLength = 4096;
  }

  return baseInfo;
}

/**
 * Mistral model information
 */
function getMistralModelInfo(model: string) {
  const baseInfo = {
    contextLength: 32768,
    supportsImages: false,
    supportsTools: true,
    isChat: true,
    isCompletion: false,
  };

  if (model.includes('large')) {
    baseInfo.contextLength = 32768;
  } else if (model.includes('medium')) {
    baseInfo.contextLength = 32768;
  } else if (model.includes('small') || model.includes('tiny')) {
    baseInfo.contextLength = 32768;
  }

  return baseInfo;
}

/**
 * Groq model information
 */
function getGroqModelInfo(model: string) {
  const baseInfo = {
    contextLength: 8192,
    supportsImages: false,
    supportsTools: true,
    isChat: true,
    isCompletion: false,
  };

  if (model.includes('32768')) {
    baseInfo.contextLength = 32768;
  } else if (model.includes('8192')) {
    baseInfo.contextLength = 8192;
  } else if (model.includes('4096')) {
    baseInfo.contextLength = 4096;
  }

  return baseInfo;
}

/**
 * Calculate token count estimate for text
 */
export function estimateTokenCount(text: string): number {
  // Rough estimation: ~4 characters per token for English text
  return Math.ceil(text.length / 4);
}

/**
 * Check if context fits within model limits
 */
export function checkContextLimit(
  provider: string,
  model: string,
  messages: Array<{ content: string }>,
): {
  fits: boolean;
  estimatedTokens: number;
  maxTokens: number;
  usage: number; // Percentage
} {
  const modelInfo = getModelInfo(provider, model);
  const totalText = messages.map(m => m.content).join('\n');
  const estimatedTokens = estimateTokenCount(totalText);
  const maxTokens = modelInfo.contextLength;
  const usage = (estimatedTokens / maxTokens) * 100;

  return {
    fits: estimatedTokens <= maxTokens * 0.9, // Leave 10% buffer
    estimatedTokens,
    maxTokens,
    usage,
  };
}

/**
 * Get recommended models for a provider
 */
export function getRecommendedModels(provider: string): string[] {
  const providerConfig = getProvider(provider);
  if (!providerConfig) {
    return [];
  }

  // Return top 3-5 recommended models
  const allModels = providerConfig.models || [];
  
  switch (provider.toLowerCase()) {
  case 'openai':
    return ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'];
  case 'gemini':
    return ['gemini-1.5-pro', 'gemini-pro', 'gemini-1.5-flash'];
  case 'mistral':
    return ['mistral-large-latest', 'mixtral-8x7b-instruct', 'mistral-medium'];
  case 'ollama':
    return ['llama2', 'codellama', 'mistral'];
  default:
    return allModels.slice(0, 5);
  }
}

/**
 * Clear model cache
 */
export function clearModelCache(): void {
  modelCache.clear();
}

/**
 * Get cache statistics
 */
export function getCacheStats(): {
  providers: number;
  totalModels: number;
  oldestCache: number;
  newestCache: number;
  } {
  const now = Date.now();
  let totalModels = 0;
  let oldestCache = now;
  let newestCache = 0;

  for (const [, cache] of modelCache) {
    totalModels += cache.models.length;
    oldestCache = Math.min(oldestCache, cache.timestamp);
    newestCache = Math.max(newestCache, cache.timestamp);
  }

  return {
    providers: modelCache.size,
    totalModels,
    oldestCache: oldestCache === now ? 0 : oldestCache,
    newestCache,
  };
}
