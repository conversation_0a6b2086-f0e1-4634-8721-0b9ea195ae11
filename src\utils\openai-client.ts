/**
 * OpenAI Client Factory
 * 
 * Creates provider-specific OpenAI client instances with proper configuration
 * for different AI providers using the OpenAI-compatible API format.
 */

import { OpenAI } from 'openai';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { getApiKey, getBaseUrl } from './config.js';
import { getProviderWithOverrides } from './providers.js';
import type { AppConfig } from '../types/index.js';

export interface ClientConfig {
  provider?: string;
  model?: string;
  apiKey?: string;
  baseURL?: string;
  timeout?: number;
  organization?: string;
  project?: string;
  defaultHeaders?: Record<string, string>;
}

/**
 * Create an OpenAI client instance for the specified provider
 */
export function createOpenAIClient(config: ClientConfig = {}): OpenAI {
  const provider = config.provider || 'openai';
  const apiKey = config.apiKey || getApiKey(provider);
  const baseURL = config.baseURL || getBaseUrl(provider);

  if (!apiKey) {
    throw new Error(`API key not found for provider: ${provider}. Please set the appropriate environment variable.`);
  }

  // Get proxy configuration if available
  const proxyUrl = process.env.HTTPS_PROXY || process.env.HTTP_PROXY;
  const httpAgent = proxyUrl ? new HttpsProxyAgent(proxyUrl) : undefined;

  // Provider-specific configuration
  const clientConfig: ConstructorParameters<typeof OpenAI>[0] = {
    apiKey,
    baseURL,
    timeout: config.timeout || 30000,
    httpAgent,
  };

  // Add provider-specific headers and configuration
  switch (provider.toLowerCase()) {
  case 'azure':
    // Azure OpenAI specific configuration
    clientConfig.defaultQuery = { 'api-version': '2024-02-15-preview' };
    clientConfig.defaultHeaders = {
      'api-key': apiKey,
      ...config.defaultHeaders,
    };
    break;

  case 'gemini':
    // Google Gemini specific configuration
    clientConfig.defaultHeaders = {
      'x-goog-api-key': apiKey,
      ...config.defaultHeaders,
    };
    break;

  case 'mistral':
    // Mistral AI specific configuration
    clientConfig.defaultHeaders = {
      'Authorization': `Bearer ${apiKey}`,
      ...config.defaultHeaders,
    };
    break;

  case 'groq':
    // Groq specific configuration
    clientConfig.defaultHeaders = {
      'Authorization': `Bearer ${apiKey}`,
      ...config.defaultHeaders,
    };
    break;

  case 'openrouter':
    // OpenRouter specific configuration
    clientConfig.defaultHeaders = {
      'Authorization': `Bearer ${apiKey}`,
      'HTTP-Referer': 'https://github.com/kritrima/kritrima-ai-cli',
      'X-Title': 'Kritrima AI CLI',
      ...config.defaultHeaders,
    };
    break;

  case 'xai':
    // xAI specific configuration
    clientConfig.defaultHeaders = {
      'Authorization': `Bearer ${apiKey}`,
      ...config.defaultHeaders,
    };
    break;

  case 'deepseek':
    // DeepSeek specific configuration
    clientConfig.defaultHeaders = {
      'Authorization': `Bearer ${apiKey}`,
      ...config.defaultHeaders,
    };
    break;

  case 'arceeai':
    // ArceeAI specific configuration
    clientConfig.defaultHeaders = {
      'Authorization': `Bearer ${apiKey}`,
      ...config.defaultHeaders,
    };
    break;

  case 'ollama':
    // Ollama specific configuration (usually no auth required for local)
    if (apiKey && apiKey !== 'not-required') {
      clientConfig.defaultHeaders = {
        'Authorization': `Bearer ${apiKey}`,
        ...config.defaultHeaders,
      };
    } else {
      clientConfig.defaultHeaders = config.defaultHeaders;
    }
    break;

  default:
    // OpenAI and OpenAI-compatible providers
    if (config.organization) {
      clientConfig.organization = config.organization;
    }
    if (config.project) {
      clientConfig.project = config.project;
    }
    clientConfig.defaultHeaders = {
      'Authorization': `Bearer ${apiKey}`,
      ...config.defaultHeaders,
    };
    break;
  }

  return new OpenAI(clientConfig);
}

/**
 * Create client from app configuration
 */
export function createClientFromConfig(appConfig: AppConfig): OpenAI {
  return createOpenAIClient({
    provider: appConfig.provider,
    model: appConfig.model,
    timeout: appConfig.timeout,
  });
}

/**
 * Test client connection and authentication
 */
export async function testClientConnection(client: OpenAI, model?: string): Promise<boolean> {
  try {
    // Try to list models or make a simple request
    const response = await client.models.list();
    return response.data.length > 0;
  } catch (error) {
    // If models endpoint fails, try a simple chat completion
    try {
      await client.chat.completions.create({
        model: model || 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'test' }],
        max_tokens: 1,
      });
      return true;
    } catch (chatError) {
      console.error('Client connection test failed:', error);
      return false;
    }
  }
}

/**
 * Get client capabilities based on provider
 */
export function getClientCapabilities(provider: string): {
  supportsImages: boolean;
  supportsTools: boolean;
  supportsStreaming: boolean;
  maxContextLength: number;
} {
  const providerConfig = getProviderWithOverrides(provider);
  
  return {
    supportsImages: providerConfig?.supportsImages || false,
    supportsTools: providerConfig?.supportsTools || false,
    supportsStreaming: true, // Most providers support streaming
    maxContextLength: providerConfig?.maxContextLength || 4096,
  };
}

/**
 * Validate client configuration
 */
export function validateClientConfig(config: ClientConfig): string[] {
  const errors: string[] = [];

  if (!config.provider) {
    errors.push('Provider is required');
  }

  const apiKey = config.apiKey || getApiKey(config.provider || 'openai');
  if (!apiKey && config.provider !== 'ollama') {
    errors.push(`API key is required for provider: ${config.provider}`);
  }

  const baseURL = config.baseURL || getBaseUrl(config.provider || 'openai');
  if (!baseURL) {
    errors.push('Base URL is required');
  }

  if (config.timeout && config.timeout < 1000) {
    errors.push('Timeout must be at least 1000ms');
  }

  return errors;
}

/**
 * Create multiple clients for different providers
 */
export function createMultipleClients(providers: string[]): Record<string, OpenAI> {
  const clients: Record<string, OpenAI> = {};

  for (const provider of providers) {
    try {
      clients[provider] = createOpenAIClient({ provider });
    } catch (error) {
      console.warn(`Failed to create client for provider ${provider}:`, error);
    }
  }

  return clients;
}

/**
 * Get recommended timeout for provider
 */
export function getRecommendedTimeout(provider: string): number {
  switch (provider.toLowerCase()) {
  case 'ollama':
    return 60000; // Local models may be slower
  case 'groq':
    return 15000; // Groq is typically fast
  case 'openai':
  case 'azure':
    return 30000; // Standard timeout
  default:
    return 30000; // Default timeout
  }
}
