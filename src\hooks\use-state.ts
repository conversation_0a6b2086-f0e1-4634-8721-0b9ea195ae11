/**
 * Simple state management hook for terminal UI components
 * Provides React-like useState functionality for blessed.js components
 */

import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { homedir } from 'os';

export type StateUpdater<T> = (value: T | ((prev: T) => T)) => void;

export interface StateHook<T> {
  value: T;
  setValue: StateUpdater<T>;
}

/**
 * Create a state hook with getter and setter
 */
export function useState<T>(initialValue: T): [() => T, StateUpdater<T>] {
  let currentValue = initialValue;
  const listeners = new Set<() => void>();

  const getValue = (): T => currentValue;

  const setValue: StateUpdater<T> = (value) => {
    const newValue = typeof value === 'function' 
      ? (value as (prev: T) => T)(currentValue)
      : value;
    
    if (newValue !== currentValue) {
      currentValue = newValue;
      // Notify all listeners
      listeners.forEach(listener => listener());
    }
  };

  const subscribe = (listener: () => void): (() => void) => {
    listeners.add(listener);
    return () => listeners.delete(listener);
  };

  // Attach subscribe method to getValue for component integration
  (getValue as any).subscribe = subscribe;

  return [getValue, setValue];
}

/**
 * Create a state manager for complex state objects
 */
export class StateManager<T extends Record<string, any>> {
  private state: T;
  private listeners = new Set<(state: T) => void>();

  constructor(initialState: T) {
    this.state = { ...initialState };
  }

  getState(): T {
    return { ...this.state };
  }

  setState(updates: Partial<T> | ((prev: T) => Partial<T>)): void {
    const newUpdates = typeof updates === 'function' 
      ? updates(this.state)
      : updates;

    const newState = { ...this.state, ...newUpdates };
    
    // Check if state actually changed
    if (JSON.stringify(newState) !== JSON.stringify(this.state)) {
      this.state = newState;
      this.notifyListeners();
    }
  }

  subscribe(listener: (state: T) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.state));
  }
}

/**
 * Create a reactive state hook that can trigger re-renders
 */
export function useReactiveState<T>(
  initialValue: T,
  onUpdate?: (value: T) => void,
): [() => T, StateUpdater<T>] {
  const [getValue, setValue] = useState(initialValue);

  const reactiveSetValue: StateUpdater<T> = (value) => {
    setValue(value);
    if (onUpdate) {
      onUpdate(getValue());
    }
  };

  return [getValue, reactiveSetValue];
}

/**
 * Create a computed state that depends on other state values
 */
export function useComputed<T, D extends any[]>(
  dependencies: D,
  computeFn: (...deps: D) => T,
): () => T {
  let cachedValue: T;
  let lastDeps: D;

  return () => {
    // Check if dependencies changed
    if (!lastDeps || dependencies.some((dep, i) => dep !== lastDeps[i])) {
      cachedValue = computeFn(...dependencies);
      lastDeps = [...dependencies] as D;
    }
    return cachedValue;
  };
}

/**
 * Create a state hook with persistence to localStorage-like storage
 */
export function usePersistedState<T>(
  key: string,
  initialValue: T,
  storage?: {
    getItem: (key: string) => string | null;
    setItem: (key: string, value: string) => void;
  },
): [() => T, StateUpdater<T>] {
  const defaultStorage = {
    getItem: (key: string) => {
      try {
        // Simple file-based storage for CLI
        const storageDir = join(homedir(), '.kritrima-ai', 'state');
        const filePath = join(storageDir, `${key}.json`);

        if (existsSync(filePath)) {
          return readFileSync(filePath, 'utf8');
        }
        return null;
      } catch {
        return null;
      }
    },
    setItem: (key: string, value: string) => {
      try {
        const storageDir = join(homedir(), '.kritrima-ai', 'state');

        // Ensure directory exists
        mkdirSync(storageDir, { recursive: true });

        const filePath = join(storageDir, `${key}.json`);
        writeFileSync(filePath, value, 'utf8');
      } catch {
        // Ignore storage errors
      }
    },
  };

  const store = storage || defaultStorage;

  // Load initial value from storage
  let storedValue: T;
  try {
    const stored = store.getItem(key);
    storedValue = stored ? JSON.parse(stored) : initialValue;
  } catch {
    storedValue = initialValue;
  }

  const [getValue, setValueInternal] = useState(storedValue);

  const setValue: StateUpdater<T> = (value) => {
    setValueInternal(value);
    
    // Persist to storage
    try {
      const newValue = typeof value === 'function' 
        ? (value as (prev: T) => T)(getValue())
        : value;
      store.setItem(key, JSON.stringify(newValue));
    } catch {
      // Ignore storage errors
    }
  };

  return [getValue, setValue];
}

/**
 * Create a debounced state hook that delays updates
 */
export function useDebouncedState<T>(
  initialValue: T,
  delay: number = 300,
): [() => T, StateUpdater<T>, () => T] {
  const [getValue, setValue] = useState(initialValue);
  const [getDebouncedValue, setDebouncedValue] = useState(initialValue);
  
  let timeoutId: NodeJS.Timeout | null = null;

  const setValueWithDebounce: StateUpdater<T> = (value) => {
    setValue(value);
    
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = setTimeout(() => {
      setDebouncedValue(getValue());
    }, delay);
  };

  return [getValue, setValueWithDebounce, getDebouncedValue];
}
