/**
 * Confirmation Hook
 * 
 * Manages confirmation queue and provides promise-based confirmation API
 * for command approval and user interactions.
 */

import { EventEmitter } from 'events';
import blessed from 'blessed';
import type { BlessedElement } from '../types/blessed-extensions.js';

export interface ConfirmationResult {
  approved: boolean;
  reason?: string;
  always?: boolean;
}

export interface ConfirmationRequest {
  id: string;
  prompt: BlessedElement;
  explanation?: string;
  resolve: (result: ConfirmationResult) => void;
  reject: (error: Error) => void;
}

class ConfirmationManager extends EventEmitter {
  private queue: ConfirmationRequest[] = [];
  private currentRequest: ConfirmationRequest | null = null;
  private isProcessing = false;

  /**
   * Request confirmation from user
   */
  async requestConfirmation(
    prompt: BlessedElement,
    explanation?: string,
  ): Promise<ConfirmationResult> {
    return new Promise((resolve, reject) => {
      const request: ConfirmationRequest = {
        id: Math.random().toString(36).substr(2, 9),
        prompt,
        explanation,
        resolve,
        reject,
      };

      this.queue.push(request);
      this.processQueue();
    });
  }

  /**
   * Submit confirmation result
   */
  submitConfirmation(result: ConfirmationResult): void {
    if (!this.currentRequest) {
      return;
    }

    const request = this.currentRequest;
    this.currentRequest = null;
    this.isProcessing = false;

    request.resolve(result);
    this.processQueue();
  }

  /**
   * Cancel current confirmation
   */
  cancelConfirmation(reason?: string): void {
    if (!this.currentRequest) {
      return;
    }

    const request = this.currentRequest;
    this.currentRequest = null;
    this.isProcessing = false;

    request.reject(new Error(reason || 'Confirmation cancelled'));
    this.processQueue();
  }

  /**
   * Get current confirmation request
   */
  getCurrentRequest(): ConfirmationRequest | null {
    return this.currentRequest;
  }

  /**
   * Check if confirmation is pending
   */
  isPending(): boolean {
    return this.isProcessing || this.queue.length > 0;
  }

  /**
   * Clear all pending confirmations
   */
  clearQueue(): void {
    // Reject all pending requests
    for (const request of this.queue) {
      request.reject(new Error('Confirmation queue cleared'));
    }

    if (this.currentRequest) {
      this.currentRequest.reject(new Error('Confirmation cancelled'));
      this.currentRequest = null;
    }

    this.queue = [];
    this.isProcessing = false;
  }

  /**
   * Process confirmation queue
   */
  private processQueue(): void {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;
    this.currentRequest = this.queue.shift()!;
    
    this.emit('confirmation-requested', this.currentRequest);
  }
}

// Global confirmation manager instance
const confirmationManager = new ConfirmationManager();

/**
 * Use confirmation hook
 */
export function useConfirmation(): {
  submitConfirmation: (result: ConfirmationResult) => void;
  requestConfirmation: (
    prompt: BlessedElement,
    explanation?: string,
  ) => Promise<ConfirmationResult>;
  confirmationPrompt: BlessedElement | null;
  explanation?: string;
  isPending: boolean;
  } {
  const currentRequest = confirmationManager.getCurrentRequest();

  return {
    submitConfirmation: (result: ConfirmationResult) => {
      confirmationManager.submitConfirmation(result);
    },
    requestConfirmation: (prompt: BlessedElement, explanation?: string) => {
      return confirmationManager.requestConfirmation(prompt, explanation);
    },
    confirmationPrompt: currentRequest?.prompt || null,
    explanation: currentRequest?.explanation,
    isPending: confirmationManager.isPending(),
  };
}

/**
 * Create confirmation prompt element
 */
export function createConfirmationPrompt(
  screen: blessed.Widgets.Screen,
  message: string,
  options: {
    title?: string;
    width?: string | number;
    height?: string | number;
    border?: boolean;
  } = {},
): blessed.Widgets.BoxElement {
  const prompt = blessed.box({
    parent: screen,
    top: 'center',
    left: 'center',
    width: options.width || '60%',
    height: options.height || 'shrink',
    content: message,
    tags: true,
    border: options.border !== false ? {
      type: 'line',
    } : undefined,
    style: {
      fg: 'white',
      bg: 'blue',
      border: {
        fg: 'cyan',
      },
    },
    padding: {
      top: 1,
      bottom: 1,
      left: 2,
      right: 2,
    },
  });

  if (options.title) {
    prompt.setLabel(options.title);
  }

  return prompt;
}

/**
 * Create yes/no confirmation dialog
 */
export function createYesNoDialog(
  screen: blessed.Widgets.Screen,
  message: string,
  title?: string,
): Promise<boolean> {
  return new Promise((resolve) => {
    const dialog = blessed.question({
      parent: screen,
      top: 'center',
      left: 'center',
      width: '50%',
      height: 'shrink',
      content: message,
      tags: true,
      border: {
        type: 'line',
      },
      style: {
        fg: 'white',
        bg: 'blue',
        border: {
          fg: 'cyan',
        },
      },
    });

    if (title) {
      dialog.setLabel(title);
    }

    dialog.ask(message, (err, value) => {
      dialog.destroy();
      screen.render();
      resolve(Boolean(value));
    });

    screen.render();
  });
}

/**
 * Create input dialog
 */
export function createInputDialog(
  screen: blessed.Widgets.Screen,
  message: string,
  title?: string,
  defaultValue?: string,
): Promise<string | null> {
  return new Promise((resolve) => {
    const dialog = blessed.prompt({
      parent: screen,
      top: 'center',
      left: 'center',
      width: '50%',
      height: 'shrink',
      content: message,
      tags: true,
      border: {
        type: 'line',
      },
      style: {
        fg: 'white',
        bg: 'blue',
        border: {
          fg: 'cyan',
        },
      },
    });

    if (title) {
      dialog.setLabel(title);
    }

    dialog.input(message, defaultValue || '', (err, value) => {
      dialog.destroy();
      screen.render();
      resolve(value || null);
    });

    screen.render();
  });
}

export default confirmationManager;
