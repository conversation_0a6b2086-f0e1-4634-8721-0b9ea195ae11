/**
 * Terminal Size Hook
 * 
 * Manages terminal size detection and resize events with proper cleanup
 * and fallback values for different environments.
 */

import { EventEmitter } from 'events';

const TERMINAL_PADDING_X = 4;
const TERMINAL_PADDING_Y = 2;

export interface TerminalSize {
  columns: number;
  rows: number;
}

class TerminalSizeManager {
  private currentSize: TerminalSize;
  private listeners: Set<(size: TerminalSize) => void> = new Set();
  private resizeHandler: (() => void) | null = null;
  private eventEmitter: EventEmitter;

  constructor() {
    this.eventEmitter = new EventEmitter();
    this.currentSize = this.detectSize();
    this.setupResizeHandler();
  }

  /**
   * Get current terminal size
   */
  getSize(): TerminalSize {
    return { ...this.currentSize };
  }

  /**
   * Add size change listener
   */
  addListener(callback: (size: TerminalSize) => void): () => void {
    this.listeners.add(callback);
    
    // Return cleanup function
    return () => {
      this.listeners.delete(callback);
    };
  }

  /**
   * Detect current terminal size
   */
  private detectSize(): TerminalSize {
    const columns = (process.stdout.columns || 80) - TERMINAL_PADDING_X;
    const rows = (process.stdout.rows || 24) - TERMINAL_PADDING_Y;

    return {
      columns: Math.max(columns, 40), // Minimum width
      rows: Math.max(rows, 10),       // Minimum height
    };
  }

  /**
   * Setup resize event handler
   */
  private setupResizeHandler(): void {
    if (this.resizeHandler) {
      return;
    }

    this.resizeHandler = () => {
      const newSize = this.detectSize();
      
      // Only emit if size actually changed
      if (newSize.columns !== this.currentSize.columns || 
          newSize.rows !== this.currentSize.rows) {
        this.currentSize = newSize;
        this.notifyListeners();
      }
    };

    // Listen for resize events
    process.stdout.on('resize', this.resizeHandler);
    
    // Also listen on stderr as fallback
    if (process.stderr && (process.stderr as any) !== (process.stdout as any)) {
      process.stderr.on('resize', this.resizeHandler);
    }
  }

  /**
   * Notify all listeners of size change
   */
  private notifyListeners(): void {
    for (const listener of this.listeners) {
      try {
        listener(this.currentSize);
      } catch (error) {
        console.error('Error in terminal size listener:', error);
      }
    }
    
    this.eventEmitter.emit('resize', this.currentSize);
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.resizeHandler) {
      process.stdout.off('resize', this.resizeHandler);
      
      if (process.stderr && (process.stderr as any) !== (process.stdout as any)) {
        process.stderr.off('resize', this.resizeHandler);
      }
      
      this.resizeHandler = null;
    }
    
    this.listeners.clear();
    this.eventEmitter.removeAllListeners();
  }
}

// Global terminal size manager
const terminalSizeManager = new TerminalSizeManager();

/**
 * Terminal size hook
 */
export function useTerminalSize(): { 
  columns: number; 
  rows: number;
  addListener: (callback: (size: TerminalSize) => void) => () => void;
  } {
  const size = terminalSizeManager.getSize();
  
  return {
    columns: size.columns,
    rows: size.rows,
    addListener: (callback: (size: TerminalSize) => void) => {
      return terminalSizeManager.addListener(callback);
    },
  };
}

/**
 * Get terminal size with custom padding
 */
export function getTerminalSize(paddingX = 0, paddingY = 0): TerminalSize {
  const baseSize = terminalSizeManager.getSize();
  
  return {
    columns: Math.max(baseSize.columns - paddingX, 20),
    rows: Math.max(baseSize.rows - paddingY, 5),
  };
}

/**
 * Check if terminal is wide enough for feature
 */
export function isTerminalWideEnough(minColumns = 80): boolean {
  return terminalSizeManager.getSize().columns >= minColumns;
}

/**
 * Check if terminal is tall enough for feature
 */
export function isTerminalTallEnough(minRows = 24): boolean {
  return terminalSizeManager.getSize().rows >= minRows;
}

/**
 * Get optimal layout dimensions
 */
export function getOptimalLayout(): {
  chatHeight: number;
  inputHeight: number;
  statusHeight: number;
  sidebarWidth: number;
  } {
  const { columns, rows } = terminalSizeManager.getSize();
  
  const statusHeight = 1;
  const inputHeight = Math.min(Math.max(Math.floor(rows * 0.15), 3), 8);
  const chatHeight = rows - inputHeight - statusHeight;
  const sidebarWidth = Math.min(Math.max(Math.floor(columns * 0.25), 20), 40);
  
  return {
    chatHeight,
    inputHeight,
    statusHeight,
    sidebarWidth,
  };
}

/**
 * Cleanup terminal size manager
 */
export function cleanupTerminalSize(): void {
  terminalSizeManager.cleanup();
}

// Cleanup on process exit
process.on('exit', cleanupTerminalSize);
process.on('SIGINT', cleanupTerminalSize);
process.on('SIGTERM', cleanupTerminalSize);

export default terminalSizeManager;
