/**
 * Git Repository Detection
 * 
 * Fast and reliable Git repository detection using git rev-parse.
 * Works across different Git versions and configurations.
 */

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';

/**
 * Check if directory is inside a Git repository
 */
export function checkInGit(workdir: string): boolean {
  try {
    // Method 1: Use git rev-parse --is-inside-work-tree
    const result = execSync('git rev-parse --is-inside-work-tree', {
      cwd: workdir,
      stdio: 'pipe',
      encoding: 'utf8',
      timeout: 5000, // 5 second timeout
    });

    return result.trim() === 'true';
  } catch (_error) {
    // Method 2: Fallback to checking for .git directory
    return checkGitDirectoryExists(workdir);
  }
}

/**
 * Check for .git directory existence (fallback method)
 */
function checkGitDirectoryExists(workdir: string): boolean {
  let currentDir = workdir;
  const maxDepth = 10; // Prevent infinite loops
  let depth = 0;

  while (depth < maxDepth) {
    const gitDir = join(currentDir, '.git');
    
    if (existsSync(gitDir)) {
      return true;
    }

    // Move up one directory
    const parentDir = join(currentDir, '..');
    
    // Check if we've reached the root
    if (parentDir === currentDir) {
      break;
    }
    
    currentDir = parentDir;
    depth++;
  }

  return false;
}

/**
 * Get Git repository root directory
 */
export function getGitRoot(workdir: string): string | null {
  try {
    const result = execSync('git rev-parse --show-toplevel', {
      cwd: workdir,
      stdio: 'pipe',
      encoding: 'utf8',
      timeout: 5000,
    });

    return result.trim();
  } catch (_error) {
    return null;
  }
}

/**
 * Check if directory has uncommitted changes
 */
export function hasUncommittedChanges(workdir: string): boolean {
  try {
    const result = execSync('git status --porcelain', {
      cwd: workdir,
      stdio: 'pipe',
      encoding: 'utf8',
      timeout: 5000,
    });

    return result.trim().length > 0;
  } catch (_error) {
    return false;
  }
}

/**
 * Get current Git branch name
 */
export function getCurrentBranch(workdir: string): string | null {
  try {
    const result = execSync('git rev-parse --abbrev-ref HEAD', {
      cwd: workdir,
      stdio: 'pipe',
      encoding: 'utf8',
      timeout: 5000,
    });

    return result.trim();
  } catch (_error) {
    return null;
  }
}

/**
 * Get Git repository status information
 */
export function getGitStatus(workdir: string): {
  isGitRepo: boolean;
  root?: string;
  branch?: string;
  hasChanges?: boolean;
  ahead?: number;
  behind?: number;
} {
  const isGitRepo = checkInGit(workdir);
  
  if (!isGitRepo) {
    return { isGitRepo: false };
  }

  const root = getGitRoot(workdir);
  const branch = getCurrentBranch(workdir);
  const hasChanges = hasUncommittedChanges(workdir);
  
  // Get ahead/behind information
  let ahead = 0;
  let behind = 0;
  
  try {
    const result = execSync('git rev-list --count --left-right @{upstream}...HEAD', {
      cwd: workdir,
      stdio: 'pipe',
      encoding: 'utf8',
      timeout: 5000,
    });

    const [behindStr, aheadStr] = result.trim().split('\t');
    behind = parseInt(behindStr, 10) || 0;
    ahead = parseInt(aheadStr, 10) || 0;
  } catch (_error) {
    // Ignore errors (e.g., no upstream branch)
  }

  return {
    isGitRepo: true,
    root: root || undefined,
    branch: branch || undefined,
    hasChanges,
    ahead,
    behind,
  };
}

/**
 * Check if file is tracked by Git
 */
export function isFileTracked(filePath: string, workdir: string): boolean {
  try {
    execSync(`git ls-files --error-unmatch "${filePath}"`, {
      cwd: workdir,
      stdio: 'pipe',
      timeout: 5000,
    });

    return true;
  } catch (_error) {
    return false;
  }
}

/**
 * Check if file is ignored by Git
 */
export function isFileIgnored(filePath: string, workdir: string): boolean {
  try {
    const result = execSync(`git check-ignore "${filePath}"`, {
      cwd: workdir,
      stdio: 'pipe',
      encoding: 'utf8',
      timeout: 5000,
    });

    return result.trim().length > 0;
  } catch (_error) {
    return false;
  }
}

/**
 * Get list of modified files
 */
export function getModifiedFiles(workdir: string): string[] {
  try {
    const result = execSync('git diff --name-only', {
      cwd: workdir,
      stdio: 'pipe',
      encoding: 'utf8',
      timeout: 5000,
    });

    return result.trim().split('\n').filter(line => line.length > 0);
  } catch (_error) {
    return [];
  }
}

/**
 * Get list of staged files
 */
export function getStagedFiles(workdir: string): string[] {
  try {
    const result = execSync('git diff --cached --name-only', {
      cwd: workdir,
      stdio: 'pipe',
      encoding: 'utf8',
      timeout: 5000,
    });

    return result.trim().split('\n').filter(line => line.length > 0);
  } catch (_error) {
    return [];
  }
}

/**
 * Get list of untracked files
 */
export function getUntrackedFiles(workdir: string): string[] {
  try {
    const result = execSync('git ls-files --others --exclude-standard', {
      cwd: workdir,
      stdio: 'pipe',
      encoding: 'utf8',
      timeout: 5000,
    });

    return result.trim().split('\n').filter(line => line.length > 0);
  } catch (_error) {
    return [];
  }
}

/**
 * Get comprehensive Git file status
 */
export function getFileStatus(workdir: string): {
  modified: string[];
  staged: string[];
  untracked: string[];
  total: number;
} {
  const modified = getModifiedFiles(workdir);
  const staged = getStagedFiles(workdir);
  const untracked = getUntrackedFiles(workdir);

  return {
    modified,
    staged,
    untracked,
    total: modified.length + staged.length + untracked.length,
  };
}

/**
 * Validate Git installation
 */
export function validateGitInstallation(): boolean {
  try {
    execSync('git --version', {
      stdio: 'pipe',
      timeout: 5000,
    });

    return true;
  } catch (_error) {
    return false;
  }
}

/**
 * Get Git version
 */
export function getGitVersion(): string | null {
  try {
    const result = execSync('git --version', {
      stdio: 'pipe',
      encoding: 'utf8',
      timeout: 5000,
    });

    return result.trim();
  } catch (_error) {
    return null;
  }
}
