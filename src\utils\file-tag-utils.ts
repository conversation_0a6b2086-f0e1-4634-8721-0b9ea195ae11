/**
 * File Tag Utilities
 * 
 * Handles expansion and collapse of file tags (@file.txt) to XML blocks
 * for including file contents in AI context.
 */

import { readFileSync, existsSync, statSync, readdirSync } from 'fs';
import { resolve, relative, basename, join } from 'path';
import { logError, logDebug } from './logger/log.js';
import { toError, getErrorMessage } from './error-utils.js';

const FILE_TAG_REGEX = /@([^\s@]+)/g;
const XML_BLOCK_REGEX = /<file path="([^"]+)">([\s\S]*?)<\/file>/g;
const MAX_FILE_SIZE = 1024 * 1024; // 1MB limit

/**
 * Expand file tags to XML blocks
 */
export async function expandFileTags(text: string, workingDir = process.cwd()): Promise<string> {
  let expandedText = text;
  const matches = Array.from(text.matchAll(FILE_TAG_REGEX));
  
  for (const match of matches) {
    const filePath = match[1];
    const fullMatch = match[0];
    
    try {
      const xmlBlock = await createFileXmlBlock(filePath, workingDir);
      expandedText = expandedText.replace(fullMatch, xmlBlock);
      logDebug(`Expanded file tag: ${filePath}`);
    } catch (error) {
      logError(`Failed to expand file tag: ${filePath}`, toError(error));
      // Replace with error message
      const errorBlock = `<file path="${filePath}" error="true">Error: ${getErrorMessage(error)}</file>`;
      expandedText = expandedText.replace(fullMatch, errorBlock);
    }
  }
  
  return expandedText;
}

/**
 * Collapse XML blocks back to file tags
 */
export function collapseXmlBlocks(text: string): string {
  return text.replace(XML_BLOCK_REGEX, (match, path) => {
    return `@${path}`;
  });
}

/**
 * Create XML block for file
 */
async function createFileXmlBlock(filePath: string, workingDir: string): Promise<string> {
  const resolvedPath = resolve(workingDir, filePath);
  
  // Check if file exists
  if (!existsSync(resolvedPath)) {
    throw new Error(`File not found: ${filePath}`);
  }
  
  // Check file size
  const stats = statSync(resolvedPath);
  if (stats.size > MAX_FILE_SIZE) {
    throw new Error(`File too large: ${filePath} (${formatFileSize(stats.size)})`);
  }
  
  // Check if it's a file (not directory)
  if (!stats.isFile()) {
    throw new Error(`Not a file: ${filePath}`);
  }
  
  // Read file content
  let content: string;
  try {
    content = readFileSync(resolvedPath, 'utf-8');
  } catch (error) {
    // Try reading as binary and convert to base64 for non-text files
    if (isBinaryFile(resolvedPath)) {
      const buffer = readFileSync(resolvedPath);
      content = `[Binary file - ${formatFileSize(buffer.length)}]\nBase64: ${buffer.toString('base64')}`;
    } else {
      throw new Error(`Failed to read file: ${getErrorMessage(error)}`);
    }
  }
  
  // Escape XML content
  const escapedContent = escapeXmlContent(content);
  
  // Use relative path for cleaner display
  const displayPath = relative(workingDir, resolvedPath);
  
  return `<file path="${displayPath}">\n${escapedContent}\n</file>`;
}

/**
 * Check if file is binary
 */
function isBinaryFile(filePath: string): boolean {
  const binaryExtensions = [
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico', '.svg',
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
    '.zip', '.rar', '.7z', '.tar', '.gz',
    '.exe', '.dll', '.so', '.dylib',
    '.mp3', '.mp4', '.avi', '.mov', '.wav',
    '.ttf', '.otf', '.woff', '.woff2',
  ];
  
  const ext = basename(filePath).toLowerCase();
  return binaryExtensions.some(binExt => ext.endsWith(binExt));
}

/**
 * Escape XML content
 */
function escapeXmlContent(content: string): string {
  return content
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
}

/**
 * Format file size for display
 */
function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

/**
 * Extract file paths from text
 */
export function extractFilePaths(text: string): string[] {
  const matches = Array.from(text.matchAll(FILE_TAG_REGEX));
  return matches.map(match => match[1]);
}

/**
 * Extract file paths from XML blocks
 */
export function extractFilePathsFromXml(text: string): string[] {
  const matches = Array.from(text.matchAll(XML_BLOCK_REGEX));
  return matches.map(match => match[1]);
}

/**
 * Validate file tag syntax
 */
export function validateFileTags(text: string, workingDir = process.cwd()): {
  valid: boolean;
  errors: Array<{ filePath: string; error: string }>;
} {
  const filePaths = extractFilePaths(text);
  const errors: Array<{ filePath: string; error: string }> = [];
  
  for (const filePath of filePaths) {
    try {
      const resolvedPath = resolve(workingDir, filePath);
      
      if (!existsSync(resolvedPath)) {
        errors.push({ filePath, error: 'File not found' });
        continue;
      }
      
      const stats = statSync(resolvedPath);
      
      if (!stats.isFile()) {
        errors.push({ filePath, error: 'Not a file' });
        continue;
      }
      
      if (stats.size > MAX_FILE_SIZE) {
        errors.push({ 
          filePath, 
          error: `File too large (${formatFileSize(stats.size)})`, 
        });
        continue;
      }
      
    } catch (error) {
      errors.push({ filePath, error: getErrorMessage(error) });
    }
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Get file suggestions for auto-completion
 */
export function getFileSuggestions(
  input: string, 
  workingDir = process.cwd(),
  maxSuggestions = 10,
): string[] {
  try {
    // Extract the partial file path after @
    const match = input.match(/@([^\s@]*)$/);
    if (!match) {
      return [];
    }
    
    const partial = match[1];
    const dirPath = partial.includes('/') ? 
      resolve(workingDir, partial.substring(0, partial.lastIndexOf('/'))) : 
      workingDir;
    
    if (!existsSync(dirPath)) {
      return [];
    }
    
    const files = readdirSync(dirPath, { withFileTypes: true });
    const suggestions: string[] = [];
    
    for (const file of files) {
      if (suggestions.length >= maxSuggestions) {
        break;
      }
      
      const fileName = file.name;
      const fullPath = join(dirPath, fileName);
      const relativePath = relative(workingDir, fullPath);
      
      // Skip hidden files and directories
      if (fileName.startsWith('.')) {
        continue;
      }
      
      // Check if it matches the partial input
      if (fileName.toLowerCase().includes(partial.toLowerCase()) ||
          relativePath.toLowerCase().includes(partial.toLowerCase())) {
        
        if (file.isFile()) {
          suggestions.push(relativePath);
        } else if (file.isDirectory()) {
          suggestions.push(relativePath + '/');
        }
      }
    }
    
    return suggestions.sort();
    
  } catch (error) {
    logError('Failed to get file suggestions', toError(error));
    return [];
  }
}
