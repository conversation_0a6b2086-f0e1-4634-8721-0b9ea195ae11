/**
 * Model Overlay Component
 * 
 * Allows users to select AI models and providers with real-time model discovery.
 */

import blessed from 'blessed';
import { providers } from '../../utils/providers.js';
import { fetchModels } from '../../utils/model-utils.js';
import { logError } from '../../utils/logger/log.js';
import type { ProviderConfig } from '../../types/index.js';

export interface ModelOverlayOptions {
  parent: blessed.Widgets.Screen;
  currentProvider: string;
  currentModel: string;
  onSelect?: (provider: string, model: string) => void;
  onClose?: () => void;
}

export class ModelOverlay {
  private screen: blessed.Widgets.Screen;
  private container: blessed.Widgets.BoxElement;
  private providerList: blessed.Widgets.ListElement;
  private modelList: blessed.Widgets.ListElement;
  private statusBar: blessed.Widgets.BoxElement;
  
  private providers: Record<string, ProviderConfig> = {};
  private models: string[] = [];
  private selectedProvider: string;
  private selectedModel: string;
  private options: ModelOverlayOptions;
  private loading = false;

  constructor(options: ModelOverlayOptions) {
    this.options = options;
    this.screen = options.parent;
    this.selectedProvider = options.currentProvider;
    this.selectedModel = options.currentModel;
    
    this.loadProviders();
    this.createComponents();
    this.setupEventHandlers();
    this.loadModels();
  }

  /**
   * Load available providers
   */
  private loadProviders(): void {
    this.providers = { ...providers };
  }

  /**
   * Create UI components
   */
  private createComponents(): void {
    // Main container
    this.container = blessed.box({
      parent: this.screen,
      top: 'center',
      left: 'center',
      width: '90%',
      height: '80%',
      border: {
        type: 'line',
      },
      style: {
        border: {
          fg: 'cyan',
        },
      },
      label: ' Model Selection ',
      tags: true,
      keys: true,
      vi: true,
    });

    // Provider list
    this.providerList = blessed.list({
      parent: this.container,
      top: 0,
      left: 0,
      width: '40%',
      height: '100%-3',
      border: {
        type: 'line',
      },
      style: {
        border: {
          fg: 'yellow',
        },
        selected: {
          bg: 'blue',
        },
      },
      label: ' Providers ',
      keys: true,
      vi: true,
      scrollable: true,
      mouse: true,
    });

    // Model list
    this.modelList = blessed.list({
      parent: this.container,
      top: 0,
      left: '40%',
      width: '60%',
      height: '100%-3',
      border: {
        type: 'line',
      },
      style: {
        border: {
          fg: 'green',
        },
        selected: {
          bg: 'blue',
        },
      },
      label: ' Models ',
      keys: true,
      vi: true,
      scrollable: true,
      mouse: true,
    });

    // Status bar
    this.statusBar = blessed.box({
      parent: this.container,
      bottom: 0,
      left: 0,
      width: '100%',
      height: 3,
      border: {
        type: 'line',
      },
      style: {
        border: {
          fg: 'gray',
        },
      },
      content: 'Tab: Switch panels | Enter: Select | Esc: Close | R: Refresh models',
      tags: true,
    });

    this.updateProviderList();
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Global key handlers
    this.container.key(['escape', 'q'], () => {
      this.close();
    });

    this.container.key(['tab'], () => {
      if ((this.providerList as any).focused) {
        this.modelList.focus();
      } else {
        this.providerList.focus();
      }
      this.screen.render();
    });

    this.container.key(['r'], () => {
      this.loadModels();
    });

    this.container.key(['enter'], () => {
      this.selectCurrent();
    });

    // Provider list handlers
    this.providerList.on('select', () => {
      this.onProviderSelect();
    });

    this.providerList.key(['j', 'down'], () => {
      this.providerList.down(1);
      this.onProviderSelect();
      this.screen.render();
    });

    this.providerList.key(['k', 'up'], () => {
      this.providerList.up(1);
      this.onProviderSelect();
      this.screen.render();
    });

    // Model list handlers
    this.modelList.on('select', () => {
      this.selectCurrent();
    });

    this.modelList.key(['j', 'down'], () => {
      this.modelList.down(1);
      this.screen.render();
    });

    this.modelList.key(['k', 'up'], () => {
      this.modelList.up(1);
      this.screen.render();
    });

    // Focus management
    this.providerList.focus();
  }

  /**
   * Update provider list
   */
  private updateProviderList(): void {
    const providerNames = Object.keys(this.providers);
    const items = providerNames.map(name => {
      const provider = this.providers[name];
      const current = name === this.selectedProvider ? '●' : '○';
      return `${current} ${provider.name}`;
    });

    this.providerList.setItems(items);
    
    // Select current provider
    const currentIndex = providerNames.indexOf(this.selectedProvider);
    if (currentIndex >= 0) {
      this.providerList.select(currentIndex);
    }

    this.screen.render();
  }

  /**
   * Handle provider selection
   */
  private onProviderSelect(): void {
    const selected = (this.providerList as any).selected;
    const providerNames = Object.keys(this.providers);
    
    if (selected >= 0 && selected < providerNames.length) {
      const newProvider = providerNames[selected];
      
      if (newProvider !== this.selectedProvider) {
        this.selectedProvider = newProvider;
        this.loadModels();
      }
    }
  }

  /**
   * Load models for current provider
   */
  private async loadModels(): Promise<void> {
    if (this.loading) {return;}
    
    this.loading = true;
    this.updateModelList(['Loading models...']);

    try {
      const models = await fetchModels(this.selectedProvider);
      this.models = models;
      this.updateModelList();
    } catch (error) {
      logError('Failed to load models', error as Error);
      this.updateModelList(['Failed to load models']);
    } finally {
      this.loading = false;
    }
  }

  /**
   * Update model list
   */
  private updateModelList(customItems?: string[]): void {
    let items: string[];
    
    if (customItems) {
      items = customItems;
    } else {
      items = this.models.map(model => {
        const current = model === this.selectedModel ? '●' : '○';
        return `${current} ${model}`;
      });
      
      if (items.length === 0) {
        items = ['No models available'];
      }
    }

    this.modelList.setItems(items);
    
    // Select current model
    if (!customItems) {
      const currentIndex = this.models.indexOf(this.selectedModel);
      if (currentIndex >= 0) {
        this.modelList.select(currentIndex);
      }
    }

    this.screen.render();
  }

  /**
   * Select current item
   */
  private selectCurrent(): void {
    if ((this.modelList as any).focused) {
      const selected = (this.modelList as any).selected;
      
      if (selected >= 0 && selected < this.models.length) {
        this.selectedModel = this.models[selected];
        
        if (this.options.onSelect) {
          this.options.onSelect(this.selectedProvider, this.selectedModel);
        }
        
        this.close();
      }
    } else if ((this.providerList as any).focused) {
      this.modelList.focus();
      this.screen.render();
    }
  }

  /**
   * Show the overlay
   */
  show(): void {
    this.container.show();
    this.providerList.focus();
    this.screen.render();
  }

  /**
   * Hide the overlay
   */
  hide(): void {
    this.container.hide();
    this.screen.render();
  }

  /**
   * Close the overlay
   */
  close(): void {
    this.hide();
    
    if (this.options.onClose) {
      this.options.onClose();
    }
  }

  /**
   * Destroy the overlay
   */
  destroy(): void {
    this.container.destroy();
  }
}

export default ModelOverlay;
