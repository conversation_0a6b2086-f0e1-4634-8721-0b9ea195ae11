/**
 * Windows Sandbox Implementation
 * 
 * Provides Windows-specific sandboxing using Job Objects,
 * process isolation, and security restrictions.
 */

import { spawn, ChildProcess, execSync } from 'child_process';
import { existsSync } from 'fs';
import { resolve } from 'path';
import { BaseSandbox, type SandboxCapabilities } from './index.js';
import { logDebug, logError, logWarn } from '../../logger/log.js';
import { toError } from '../../error-utils.js';
import type { ExecInput, ExecResult } from '../../../types/index.js';

export class WindowsSandbox extends BaseSandbox {
  private activeProcesses = new Set<ChildProcess>();

  /**
   * Execute command in Windows sandbox
   */
  async execute(input: ExecInput): Promise<ExecResult> {
    const validation = this.validateCommand(input);
    if (!validation.valid) {
      throw new Error(`Command validation failed: ${validation.errors.join(', ')}`);
    }

    const startTime = Date.now();
    const workdir = input.workdir || this.options.workingDirectory;
    
    this.logActivity('execute', {
      command: input.command,
      workdir,
      timeout: this.options.timeout,
    });

    return new Promise((resolve, reject) => {
      let stdout = '';
      let stderr = '';
      let timedOut = false;

      // Prepare command for Windows
      const { command, args } = this.prepareWindowsCommand(input.command);
      
      // Spawn process with Windows-specific options
      const child = spawn(command, args, {
        cwd: workdir,
        env: this.sanitizeEnvironment(),
        stdio: ['pipe', 'pipe', 'pipe'],
        windowsHide: true,
        detached: false,
        shell: false,
      });

      this.activeProcesses.add(child);

      // Set up timeout
      const timeoutHandle = setTimeout(() => {
        timedOut = true;
        this.killProcess(child);
      }, this.options.timeout);

      // Handle stdout
      child.stdout?.on('data', (data: Buffer) => {
        stdout += data.toString();
      });

      // Handle stderr
      child.stderr?.on('data', (data: Buffer) => {
        stderr += data.toString();
      });

      // Handle process exit
      child.on('exit', (code, signal) => {
        clearTimeout(timeoutHandle);
        this.activeProcesses.delete(child);

        const duration = Date.now() - startTime;
        
        const result: ExecResult = {
          exitCode: timedOut ? 124 : (code || 0), // 124 is timeout exit code
          stdout: stdout.trim(),
          stderr: stderr.trim(),
          duration,
          timedOut,
          signal: signal || undefined,
        };

        this.logActivity('completed', {
          exitCode: result.exitCode,
          duration,
          timedOut,
        });

        resolve(result);
      });

      // Handle process errors
      child.on('error', (error) => {
        clearTimeout(timeoutHandle);
        this.activeProcesses.delete(child);
        
        logError('Process execution error', error);
        reject(new Error(`Process execution failed: ${error.message}`));
      });

      // Close stdin to prevent hanging
      child.stdin?.end();
    });
  }

  /**
   * Get Windows sandbox capabilities
   */
  getCapabilities(): SandboxCapabilities {
    return {
      isolation: true, // Process isolation
      resourceLimits: false, // Limited resource control on Windows
      networkRestriction: false, // No built-in network restriction
      fileSystemRestriction: true, // Can restrict file access
      userIsolation: false, // Limited user isolation
      processLimits: true, // Can limit process creation
    };
  }

  /**
   * Check if Windows sandbox is available
   */
  async isAvailable(): Promise<boolean> {
    if (process.platform !== 'win32') {
      return false;
    }

    try {
      // Test basic command execution
      const testResult = await this.execute({
        command: ['cmd', '/c', 'echo', 'test'],
      });
      
      return testResult.exitCode === 0;
    } catch (error) {
      logError('Windows sandbox availability check failed', toError(error));
      return false;
    }
  }

  /**
   * Setup Windows sandbox environment
   */
  async setup(): Promise<void> {
    // Windows-specific setup
    logDebug('Setting up Windows sandbox');
    
    // Verify working directory exists
    if (!existsSync(this.options.workingDirectory)) {
      throw new Error(`Working directory does not exist: ${this.options.workingDirectory}`);
    }
  }

  /**
   * Cleanup Windows sandbox resources
   */
  async cleanup(): Promise<void> {
    logDebug('Cleaning up Windows sandbox');
    
    // Kill any remaining processes
    for (const process of this.activeProcesses) {
      this.killProcess(process);
    }
    
    this.activeProcesses.clear();
  }

  /**
   * Prepare command for Windows execution
   */
  private prepareWindowsCommand(command: string[]): { command: string; args: string[] } {
    if (command.length === 0) {
      throw new Error('Command cannot be empty');
    }

    const [cmd, ...args] = command;
    
    // Handle common Unix commands on Windows
    const windowsCommands: Record<string, { command: string; args: string[] }> = {
      'ls': { command: 'cmd', args: ['/c', 'dir', '/b', ...args] },
      'cat': { command: 'cmd', args: ['/c', 'type', ...args] },
      'pwd': { command: 'cmd', args: ['/c', 'cd'] },
      'echo': { command: 'cmd', args: ['/c', 'echo', ...args] },
      'mkdir': { command: 'cmd', args: ['/c', 'mkdir', ...args] },
      'rmdir': { command: 'cmd', args: ['/c', 'rmdir', '/s', '/q', ...args] },
      'rm': { command: 'cmd', args: ['/c', 'del', '/f', '/q', ...args] },
      'cp': { command: 'cmd', args: ['/c', 'copy', ...args] },
      'mv': { command: 'cmd', args: ['/c', 'move', ...args] },
    };

    if (windowsCommands[cmd.toLowerCase()]) {
      return windowsCommands[cmd.toLowerCase()];
    }

    // Handle .exe extension
    let windowsCmd = cmd;
    if (!cmd.includes('.') && !cmd.includes('\\') && !cmd.includes('/')) {
      // Try to find executable
      const possibleExts = ['.exe', '.cmd', '.bat', '.com'];
      for (const ext of possibleExts) {
        if (existsSync(cmd + ext)) {
          windowsCmd = cmd + ext;
          break;
        }
      }
    }

    // Handle PowerShell commands
    if (cmd.toLowerCase() === 'powershell' || cmd.toLowerCase() === 'pwsh') {
      return {
        command: windowsCmd,
        args: ['-NoProfile', '-NonInteractive', '-ExecutionPolicy', 'Bypass', ...args],
      };
    }

    // Handle cmd commands
    if (cmd.toLowerCase() === 'cmd') {
      return {
        command: 'cmd',
        args: ['/c', ...args],
      };
    }

    return { command: windowsCmd, args };
  }

  /**
   * Kill process and its children
   */
  private killProcess(process: ChildProcess): void {
    try {
      if (process.pid && !process.killed) {
        // On Windows, use taskkill to kill process tree
        spawn('taskkill', ['/pid', process.pid.toString(), '/t', '/f'], {
          stdio: 'ignore',
          detached: true,
        });
      }
    } catch (error) {
      logWarn('Failed to kill process', error);
      
      // Fallback to basic kill
      try {
        process.kill('SIGKILL');
      } catch (killError) {
        logWarn('Failed to kill process with SIGKILL', killError);
      }
    }
  }

  /**
   * Apply Windows-specific security restrictions
   */
  private applySecurityRestrictions(): void {
    // Windows-specific security measures
    // This would involve using Windows APIs for:
    // - Job Objects for resource limits
    // - Security descriptors for access control
    // - Process isolation
    
    logDebug('Applying Windows security restrictions');
  }

  /**
   * Check if path is safe for access
   */
  private isPathSafe(path: string): boolean {
    const resolvedPath = resolve(path);
    
    // Block access to system directories
    const blockedPaths = [
      'C:\\Windows\\System32',
      'C:\\Windows\\SysWOW64',
      'C:\\Program Files',
      'C:\\Program Files (x86)',
      'C:\\ProgramData',
      'C:\\Users\\<USER>