import type { TSESLint } from '@typescript-eslint/utils';
import { config } from './config-helper';
export declare const parser: TSESLint.FlatConfig.Parser;
export declare const plugin: TSESLint.FlatConfig.Plugin;
export declare const configs: {
    /**
     * Enables each the rules provided as a part of typescript-eslint. Note that many rules are not applicable in all codebases, or are meant to be configured.
     * @see {@link https://typescript-eslint.io/users/configs#all}
     */
    all: TSESLint.FlatConfig.ConfigArray;
    /**
     * A minimal ruleset that sets only the required parser and plugin options needed to run typescript-eslint.
     * We don't recommend using this directly; instead, extend from an earlier recommended rule.
     * @see {@link https://typescript-eslint.io/users/configs#base}
     */
    base: TSESLint.FlatConfig.Config;
    /**
     * A utility ruleset that will disable type-aware linting and all type-aware rules available in our project.
     * @see {@link https://typescript-eslint.io/users/configs#disable-type-checked}
     */
    disableTypeChecked: TSESLint.FlatConfig.Config;
    /**
     * This is a compatibility ruleset that:
     * - disables rules from eslint:recommended which are already handled by TypeScript.
     * - enables rules that make sense due to TS's typechecking / transpilation.
     * @see {@link https://typescript-eslint.io/users/configs/#eslint-recommended}
     */
    eslintRecommended: TSESLint.FlatConfig.Config;
    /**
     * Recommended rules for code correctness that you can drop in without additional configuration.
     * @see {@link https://typescript-eslint.io/users/configs#recommended}
     */
    recommended: TSESLint.FlatConfig.ConfigArray;
    /**
     * Contains all of `recommended` along with additional recommended rules that require type information.
     * @see {@link https://typescript-eslint.io/users/configs#recommended-type-checked}
     */
    recommendedTypeChecked: TSESLint.FlatConfig.ConfigArray;
    /**
     * A version of `recommended` that only contains type-checked rules and disables of any corresponding core ESLint rules.
     * @see {@link https://typescript-eslint.io/users/configs#recommended-type-checked-only}
     */
    recommendedTypeCheckedOnly: TSESLint.FlatConfig.ConfigArray;
    /**
     * Contains all of `recommended`, as well as additional strict rules that can also catch bugs.
     * @see {@link https://typescript-eslint.io/users/configs#strict}
     */
    strict: TSESLint.FlatConfig.ConfigArray;
    /**
     * Contains all of `recommended`, `recommended-type-checked`, and `strict`, along with additional strict rules that require type information.
     * @see {@link https://typescript-eslint.io/users/configs#strict-type-checked}
     */
    strictTypeChecked: TSESLint.FlatConfig.ConfigArray;
    /**
     * A version of `strict` that only contains type-checked rules and disables of any corresponding core ESLint rules.
     * @see {@link https://typescript-eslint.io/users/configs#strict-type-checked-only}
     */
    strictTypeCheckedOnly: TSESLint.FlatConfig.ConfigArray;
    /**
     * Rules considered to be best practice for modern TypeScript codebases, but that do not impact program logic.
     * @see {@link https://typescript-eslint.io/users/configs#stylistic}
     */
    stylistic: TSESLint.FlatConfig.ConfigArray;
    /**
     * Contains all of `stylistic`, along with additional stylistic rules that require type information.
     * @see {@link https://typescript-eslint.io/users/configs#stylistic-type-checked}
     */
    stylisticTypeChecked: TSESLint.FlatConfig.ConfigArray;
    /**
     * A version of `stylistic` that only contains type-checked rules and disables of any corresponding core ESLint rules.
     * @see {@link https://typescript-eslint.io/users/configs#stylistic-type-checked-only}
     */
    stylisticTypeCheckedOnly: TSESLint.FlatConfig.ConfigArray;
};
export type Config = TSESLint.FlatConfig.ConfigFile;
declare const _default: {
    config: typeof config;
    configs: {
        /**
         * Enables each the rules provided as a part of typescript-eslint. Note that many rules are not applicable in all codebases, or are meant to be configured.
         * @see {@link https://typescript-eslint.io/users/configs#all}
         */
        all: TSESLint.FlatConfig.ConfigArray;
        /**
         * A minimal ruleset that sets only the required parser and plugin options needed to run typescript-eslint.
         * We don't recommend using this directly; instead, extend from an earlier recommended rule.
         * @see {@link https://typescript-eslint.io/users/configs#base}
         */
        base: TSESLint.FlatConfig.Config;
        /**
         * A utility ruleset that will disable type-aware linting and all type-aware rules available in our project.
         * @see {@link https://typescript-eslint.io/users/configs#disable-type-checked}
         */
        disableTypeChecked: TSESLint.FlatConfig.Config;
        /**
         * This is a compatibility ruleset that:
         * - disables rules from eslint:recommended which are already handled by TypeScript.
         * - enables rules that make sense due to TS's typechecking / transpilation.
         * @see {@link https://typescript-eslint.io/users/configs/#eslint-recommended}
         */
        eslintRecommended: TSESLint.FlatConfig.Config;
        /**
         * Recommended rules for code correctness that you can drop in without additional configuration.
         * @see {@link https://typescript-eslint.io/users/configs#recommended}
         */
        recommended: TSESLint.FlatConfig.ConfigArray;
        /**
         * Contains all of `recommended` along with additional recommended rules that require type information.
         * @see {@link https://typescript-eslint.io/users/configs#recommended-type-checked}
         */
        recommendedTypeChecked: TSESLint.FlatConfig.ConfigArray;
        /**
         * A version of `recommended` that only contains type-checked rules and disables of any corresponding core ESLint rules.
         * @see {@link https://typescript-eslint.io/users/configs#recommended-type-checked-only}
         */
        recommendedTypeCheckedOnly: TSESLint.FlatConfig.ConfigArray;
        /**
         * Contains all of `recommended`, as well as additional strict rules that can also catch bugs.
         * @see {@link https://typescript-eslint.io/users/configs#strict}
         */
        strict: TSESLint.FlatConfig.ConfigArray;
        /**
         * Contains all of `recommended`, `recommended-type-checked`, and `strict`, along with additional strict rules that require type information.
         * @see {@link https://typescript-eslint.io/users/configs#strict-type-checked}
         */
        strictTypeChecked: TSESLint.FlatConfig.ConfigArray;
        /**
         * A version of `strict` that only contains type-checked rules and disables of any corresponding core ESLint rules.
         * @see {@link https://typescript-eslint.io/users/configs#strict-type-checked-only}
         */
        strictTypeCheckedOnly: TSESLint.FlatConfig.ConfigArray;
        /**
         * Rules considered to be best practice for modern TypeScript codebases, but that do not impact program logic.
         * @see {@link https://typescript-eslint.io/users/configs#stylistic}
         */
        stylistic: TSESLint.FlatConfig.ConfigArray;
        /**
         * Contains all of `stylistic`, along with additional stylistic rules that require type information.
         * @see {@link https://typescript-eslint.io/users/configs#stylistic-type-checked}
         */
        stylisticTypeChecked: TSESLint.FlatConfig.ConfigArray;
        /**
         * A version of `stylistic` that only contains type-checked rules and disables of any corresponding core ESLint rules.
         * @see {@link https://typescript-eslint.io/users/configs#stylistic-type-checked-only}
         */
        stylisticTypeCheckedOnly: TSESLint.FlatConfig.ConfigArray;
    };
    parser: TSESLint.Parser.LooseParserModule;
    plugin: TSESLint.FlatConfig.Plugin;
};
export default _default;
export { config, type ConfigWithExtends, type InfiniteDepthConfigWithExtends, type ConfigArray, } from './config-helper';
//# sourceMappingURL=index.d.ts.map