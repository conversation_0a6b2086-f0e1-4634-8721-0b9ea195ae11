/**
 * Diff Overlay Component
 * 
 * Displays git diff information with syntax highlighting and navigation.
 */

import blessed from 'blessed';
import { getGitDiff } from '../../utils/get-diff.js';
import { logError } from '../../utils/logger/log.js';

export interface DiffOverlayOptions {
  parent: blessed.Widgets.Screen;
  workingDirectory?: string;
  onClose?: () => void;
}

export class DiffOverlay {
  private screen: blessed.Widgets.Screen;
  private container: blessed.Widgets.BoxElement;
  private diffBox: blessed.Widgets.BoxElement;
  private statusBar: blessed.Widgets.BoxElement;
  private options: DiffOverlayOptions;
  private loading = false;

  constructor(options: DiffOverlayOptions) {
    this.options = options;
    this.screen = options.parent;
    
    this.createComponents();
    this.setupEventHandlers();
    this.loadDiff();
  }

  /**
   * Create UI components
   */
  private createComponents(): void {
    // Main container
    this.container = blessed.box({
      parent: this.screen,
      top: 'center',
      left: 'center',
      width: '95%',
      height: '90%',
      border: {
        type: 'line',
      },
      style: {
        border: {
          fg: 'cyan',
        },
      },
      label: ' Git Diff ',
      tags: true,
      keys: true,
      vi: true,
    });

    // Diff content box
    this.diffBox = blessed.box({
      parent: this.container,
      top: 0,
      left: 0,
      width: '100%',
      height: '100%-3',
      scrollable: true,
      alwaysScroll: true,
      scrollbar: {
        ch: ' ',
        track: {
          bg: 'cyan',
        },
        style: {
          inverse: true,
        },
      },
      keys: true,
      vi: true,
      mouse: true,
      tags: true,
      style: {
        fg: 'white',
        bg: 'black',
      },
    });

    // Status bar
    this.statusBar = blessed.box({
      parent: this.container,
      bottom: 0,
      left: 0,
      width: '100%',
      height: 3,
      border: {
        type: 'line',
      },
      style: {
        border: {
          fg: 'gray',
        },
      },
      content: 'j/k: Scroll | R: Refresh | Esc: Close',
      tags: true,
    });
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Close handlers
    this.container.key(['escape', 'q'], () => {
      this.close();
    });

    // Refresh handler
    this.container.key(['r'], () => {
      this.loadDiff();
    });

    // Scroll handlers
    this.diffBox.key(['j', 'down'], () => {
      this.diffBox.scroll(1);
      this.screen.render();
    });

    this.diffBox.key(['k', 'up'], () => {
      this.diffBox.scroll(-1);
      this.screen.render();
    });

    this.diffBox.key(['pagedown'], () => {
      this.diffBox.scroll(10);
      this.screen.render();
    });

    this.diffBox.key(['pageup'], () => {
      this.diffBox.scroll(-10);
      this.screen.render();
    });

    this.diffBox.key(['home'], () => {
      this.diffBox.scrollTo(0);
      this.screen.render();
    });

    this.diffBox.key(['end'], () => {
      this.diffBox.scrollTo(this.diffBox.getScrollHeight());
      this.screen.render();
    });

    // Focus management
    this.diffBox.focus();
  }

  /**
   * Load git diff
   */
  private async loadDiff(): Promise<void> {
    if (this.loading) {return;}
    
    this.loading = true;
    this.updateStatus('Loading git diff...');

    try {
      const workingDir = this.options.workingDirectory || process.cwd();
      const diff = await getGitDiff(workingDir);
      
      if (diff.trim()) {
        this.displayDiff(diff);
        this.updateStatus('Git diff loaded');
      } else {
        this.displayMessage('No changes found in git repository');
        this.updateStatus('No changes');
      }
    } catch (error) {
      const err = error as Error;
      logError('Failed to load git diff', err);
      this.displayMessage(`Error loading git diff: ${err.message}`);
      this.updateStatus('Error loading diff');
    } finally {
      this.loading = false;
    }
  }

  /**
   * Display diff content with syntax highlighting
   */
  private displayDiff(diff: string): void {
    const lines = diff.split('\n');
    const formattedLines: string[] = [];

    for (const line of lines) {
      if (line.startsWith('diff --git')) {
        // File header
        formattedLines.push(`{bold}{cyan-fg}${line}{/cyan-fg}{/bold}`);
      } else if (line.startsWith('index ') || line.startsWith('@@')) {
        // Index or hunk header
        formattedLines.push(`{bold}{blue-fg}${line}{/blue-fg}{/bold}`);
      } else if (line.startsWith('+++') || line.startsWith('---')) {
        // File path headers
        formattedLines.push(`{bold}{white-fg}${line}{/white-fg}{/bold}`);
      } else if (line.startsWith('+')) {
        // Added lines
        formattedLines.push(`{green-fg}${line}{/green-fg}`);
      } else if (line.startsWith('-')) {
        // Removed lines
        formattedLines.push(`{red-fg}${line}{/red-fg}`);
      } else if (line.startsWith(' ')) {
        // Context lines
        formattedLines.push(`{gray-fg}${line}{/gray-fg}`);
      } else {
        // Other lines
        formattedLines.push(line);
      }
    }

    this.diffBox.setContent(formattedLines.join('\n'));
    this.screen.render();
  }

  /**
   * Display a message when no diff or error
   */
  private displayMessage(message: string): void {
    const content = [
      '',
      `{center}{bold}{yellow-fg}${message}{/yellow-fg}{/bold}{/center}`,
      '',
      '{center}{gray-fg}Press R to refresh or Esc to close{/gray-fg}{/center}',
    ];

    this.diffBox.setContent(content.join('\n'));
    this.screen.render();
  }

  /**
   * Update status bar
   */
  private updateStatus(status: string): void {
    this.statusBar.setContent(`${status} | j/k: Scroll | R: Refresh | Esc: Close`);
    this.screen.render();
  }

  /**
   * Show the overlay
   */
  show(): void {
    this.container.show();
    this.diffBox.focus();
    this.screen.render();
  }

  /**
   * Hide the overlay
   */
  hide(): void {
    this.container.hide();
    this.screen.render();
  }

  /**
   * Close the overlay
   */
  close(): void {
    this.hide();
    
    if (this.options.onClose) {
      this.options.onClose();
    }
  }

  /**
   * Destroy the overlay
   */
  destroy(): void {
    this.container.destroy();
  }
}

export default DiffOverlay;
