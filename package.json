{"name": "kritrima-ai-cli", "version": "1.0.0", "description": "Comprehensive AI coding assistant with autonomous agent capabilities", "main": "dist/cli.js", "bin": {"kritrima-ai": "./bin/kritrima-ai.js"}, "type": "module", "scripts": {"build": "tsc && npm run copy-assets", "copy-assets": "if exist src\\assets xcopy src\\assets dist\\assets /E /I /Q || echo No assets to copy", "dev": "tsx src/cli.tsx", "start": "node dist/cli.js", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src/**/*.ts src/**/*.tsx", "lint:fix": "eslint src/**/*.ts src/**/*.tsx --fix", "typecheck": "tsc --noEmit", "clean": "rm -rf dist", "prepare": "npm run build", "prepublishOnly": "npm run clean && npm run build && npm test"}, "keywords": ["ai", "cli", "coding-assistant", "autonomous-agent", "openai", "typescript", "terminal", "development-tools"], "author": "Kritrima AI", "license": "MIT", "engines": {"node": ">=22.0.0"}, "dependencies": {"blessed": "^0.1.81", "chalk": "^5.3.0", "chokidar": "^4.0.1", "commander": "^12.1.0", "date-fns": "^4.1.0", "debug": "^4.4.0", "dotenv": "^16.4.7", "execa": "^9.5.1", "fs-extra": "^11.2.0", "glob": "^11.0.0", "https-proxy-agent": "^7.0.5", "inquirer": "^12.1.0", "lodash": "^4.17.21", "mime-types": "^2.1.35", "nanoid": "^5.0.9", "node-notifier": "^10.0.1", "openai": "^4.67.3", "ora": "^8.1.1", "semver": "^7.6.3", "sharp": "^0.33.5", "yaml": "^2.6.1", "zod": "^3.23.8"}, "devDependencies": {"@types/blessed": "^0.1.25", "@types/debug": "^4.1.12", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.17.13", "@types/mime-types": "^2.1.4", "@types/node": "^22.10.2", "@types/semver": "^7.5.8", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "@vitest/coverage-v8": "^2.1.8", "eslint": "^9.17.0", "tsx": "^4.19.2", "typescript": "^5.8.0", "typescript-eslint": "^8.33.0", "vitest": "^2.1.8"}, "repository": {"type": "git", "url": "https://github.com/kritrima/kritrima-ai-cli.git"}, "bugs": {"url": "https://github.com/kritrima/kritrima-ai-cli/issues"}, "homepage": "https://github.com/kritrima/kritrima-ai-cli#readme"}