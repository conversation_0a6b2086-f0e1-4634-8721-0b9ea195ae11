/**
 * History Overlay Component
 * 
 * Displays command history with search functionality and selection.
 */

import blessed from 'blessed';
import { getHistory, clearHistory } from '../../utils/storage/command-history.js';
import { logDebug, logError } from '../../utils/logger/log.js';
import type { HistoryEntry } from '../../types/index.js';

export interface HistoryOverlayOptions {
  parent: blessed.Widgets.Screen;
  onSelect?: (command: string) => void;
  onClose?: () => void;
}

export class HistoryOverlay {
  private screen: blessed.Widgets.Screen;
  private container: blessed.Widgets.BoxElement;
  private list: blessed.Widgets.ListElement;
  private searchBox: blessed.Widgets.TextboxElement;
  private statusBar: blessed.Widgets.BoxElement;
  
  private history: HistoryEntry[] = [];
  private filteredHistory: HistoryEntry[] = [];
  private searchTerm = '';
  private options: HistoryOverlayOptions;

  constructor(options: HistoryOverlayOptions) {
    this.options = options;
    this.screen = options.parent;
    
    this.loadHistory();
    this.createComponents();
    this.setupEventHandlers();
    this.updateList();
  }

  /**
   * Load command history
   */
  private loadHistory(): void {
    try {
      this.history = getHistory();
      this.filteredHistory = [...this.history];
    } catch (error) {
      logError('Failed to load history', error as Error);
      this.history = [];
      this.filteredHistory = [];
    }
  }

  /**
   * Create UI components
   */
  private createComponents(): void {
    // Main container
    this.container = blessed.box({
      parent: this.screen,
      top: 'center',
      left: 'center',
      width: '80%',
      height: '80%',
      border: {
        type: 'line',
      },
      style: {
        border: {
          fg: 'cyan',
        },
      },
      label: ' Command History ',
      tags: true,
      keys: true,
      vi: true,
    });

    // Search box
    this.searchBox = blessed.textbox({
      parent: this.container,
      top: 0,
      left: 0,
      width: '100%',
      height: 3,
      border: {
        type: 'line',
      },
      style: {
        border: {
          fg: 'yellow',
        },
      },
      label: ' Search ',
      inputOnFocus: true,
      keys: true,
    });

    // History list
    this.list = blessed.list({
      parent: this.container,
      top: 3,
      left: 0,
      width: '100%',
      height: '100%-6',
      border: {
        type: 'line',
      },
      style: {
        border: {
          fg: 'white',
        },
        selected: {
          bg: 'blue',
        },
      },
      keys: true,
      vi: true,
      scrollable: true,
      alwaysScroll: true,
      mouse: true,
    });

    // Status bar
    this.statusBar = blessed.box({
      parent: this.container,
      bottom: 0,
      left: 0,
      width: '100%',
      height: 3,
      border: {
        type: 'line',
      },
      style: {
        border: {
          fg: 'gray',
        },
      },
      content: 'Enter: Select | Esc: Close | /: Search | Del: Clear History',
      tags: true,
    });

    // Keep reference for potential future use
    void this.statusBar;
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Global key handlers
    this.container.key(['escape', 'q'], () => {
      this.close();
    });

    this.container.key(['enter'], () => {
      this.selectCurrent();
    });

    this.container.key(['/'], () => {
      this.searchBox.focus();
    });

    this.container.key(['delete'], () => {
      this.clearHistory();
    });

    // Search box handlers
    this.searchBox.on('submit', (value: string) => {
      this.searchTerm = value;
      this.filterHistory();
      this.list.focus();
    });

    this.searchBox.on('cancel', () => {
      this.list.focus();
    });

    this.searchBox.key(['escape'], () => {
      this.searchBox.clearValue();
      this.searchTerm = '';
      this.filterHistory();
      this.list.focus();
    });

    // List handlers
    this.list.on('select', () => {
      this.selectCurrent();
    });

    this.list.key(['j', 'down'], () => {
      this.list.down(1);
      this.screen.render();
    });

    this.list.key(['k', 'up'], () => {
      this.list.up(1);
      this.screen.render();
    });

    // Focus management
    this.list.focus();
  }

  /**
   * Filter history based on search term
   */
  private filterHistory(): void {
    if (!this.searchTerm.trim()) {
      this.filteredHistory = [...this.history];
    } else {
      const term = this.searchTerm.toLowerCase();
      this.filteredHistory = this.history.filter(entry =>
        entry.command.toLowerCase().includes(term),
      );
    }
    
    this.updateList();
  }

  /**
   * Update list display
   */
  private updateList(): void {
    const items = this.filteredHistory.map(entry => {
      const timestamp = new Date(entry.timestamp).toLocaleString();
      const success = entry.success !== false ? '✓' : '✗';
      return `${success} ${entry.command} {gray-fg}(${timestamp}){/gray-fg}`;
    });

    this.list.setItems(items);
    
    if (items.length === 0) {
      this.list.setItems(['No commands found']);
    }

    this.screen.render();
  }

  /**
   * Select current item
   */
  private selectCurrent(): void {
    const selected = (this.list as any).selected;

    if (selected >= 0 && selected < this.filteredHistory.length) {
      const entry = this.filteredHistory[selected];

      if (this.options.onSelect) {
        this.options.onSelect(entry.command);
      }

      this.close();
    }
  }

  /**
   * Clear all history
   */
  private clearHistory(): void {
    try {
      clearHistory();
      this.history = [];
      this.filteredHistory = [];
      this.updateList();
      
      logDebug('Command history cleared');
    } catch (error) {
      logError('Failed to clear history', error as Error);
    }
  }

  /**
   * Show the overlay
   */
  show(): void {
    this.container.show();
    this.list.focus();
    this.screen.render();
  }

  /**
   * Hide the overlay
   */
  hide(): void {
    this.container.hide();
    this.screen.render();
  }

  /**
   * Close the overlay
   */
  close(): void {
    this.hide();
    
    if (this.options.onClose) {
      this.options.onClose();
    }
  }

  /**
   * Destroy the overlay
   */
  destroy(): void {
    this.container.destroy();
  }
}

export default HistoryOverlay;
