/**
 * Git Diff Utilities
 * 
 * Provides Git integration for generating diffs, checking status,
 * and understanding repository state.
 */

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { resolve } from 'path';
import { logError, logDebug } from './logger/log.js';
import { checkInGit } from './check-in-git.js';
import { toError, getErrorMessage, getErrorStatus } from './error-utils.js';
import type { DiffResult } from '../types/index.js';

export interface GitStatus {
  branch: string;
  ahead: number;
  behind: number;
  staged: string[];
  unstaged: string[];
  untracked: string[];
  conflicted: string[];
}

export interface GitDiffOptions {
  staged?: boolean;
  cached?: boolean;
  nameOnly?: boolean;
  stat?: boolean;
  unified?: number;
  ignoreWhitespace?: boolean;
  files?: string[];
}

/**
 * Get git diff for current working directory
 */
export async function getGitDiff(
  workingDirectory = process.cwd(),
  options: GitDiffOptions = {},
): Promise<string> {
  try {
    // Check if we're in a git repository
    if (!checkInGit(workingDirectory)) {
      throw new Error('Not in a Git repository');
    }

    const args = ['diff'];
    
    // Add options
    if (options.staged || options.cached) {
      args.push('--cached');
    }
    
    if (options.nameOnly) {
      args.push('--name-only');
    }
    
    if (options.stat) {
      args.push('--stat');
    }
    
    if (options.unified !== undefined) {
      args.push(`--unified=${options.unified}`);
    }
    
    if (options.ignoreWhitespace) {
      args.push('--ignore-all-space');
    }
    
    // Add specific files if provided
    if (options.files && options.files.length > 0) {
      args.push('--');
      args.push(...options.files);
    }

    const command = `git ${args.join(' ')}`;
    logDebug(`Executing git command: ${command}`);
    
    const output = execSync(command, {
      cwd: workingDirectory,
      encoding: 'utf-8',
      maxBuffer: 1024 * 1024 * 10, // 10MB buffer
    });

    return output.trim();

  } catch (error) {
    logError('Failed to get git diff', toError(error));

    const status = getErrorStatus(error);
    if (status === 128) {
      throw new Error('Git command failed - not a git repository or invalid command');
    } else if (status === 129) {
      throw new Error('Git command failed - invalid arguments');
    } else {
      throw new Error(`Git diff failed: ${getErrorMessage(error)}`);
    }
  }
}

/**
 * Get git status information
 */
export async function getGitStatus(workingDirectory = process.cwd()): Promise<GitStatus> {
  try {
    if (!checkInGit(workingDirectory)) {
      throw new Error('Not in a Git repository');
    }

    // Get branch information
    const branchOutput = execSync('git branch --show-current', {
      cwd: workingDirectory,
      encoding: 'utf-8',
    }).trim();

    // Get ahead/behind information
    let ahead = 0;
    let behind = 0;
    try {
      const trackingOutput = execSync('git status -b --porcelain=v1', {
        cwd: workingDirectory,
        encoding: 'utf-8',
      });
      
      const trackingMatch = trackingOutput.match(/\[ahead (\d+)(?:, behind (\d+))?\]/);
      if (trackingMatch) {
        ahead = parseInt(trackingMatch[1]) || 0;
        behind = parseInt(trackingMatch[2]) || 0;
      }
    } catch (error) {
      // Ignore tracking errors (e.g., no upstream)
    }

    // Get file status
    const statusOutput = execSync('git status --porcelain', {
      cwd: workingDirectory,
      encoding: 'utf-8',
    });

    const staged: string[] = [];
    const unstaged: string[] = [];
    const untracked: string[] = [];
    const conflicted: string[] = [];

    for (const line of statusOutput.split('\n')) {
      if (!line.trim()) {continue;}
      
      const status = line.slice(0, 2);
      const file = line.slice(3);
      
      // Check for conflicts
      if (status.includes('U') || status === 'AA' || status === 'DD') {
        conflicted.push(file);
        continue;
      }
      
      // Check staged changes
      if (status[0] !== ' ' && status[0] !== '?') {
        staged.push(file);
      }
      
      // Check unstaged changes
      if (status[1] !== ' ' && status[1] !== '?') {
        unstaged.push(file);
      }
      
      // Check untracked files
      if (status === '??') {
        untracked.push(file);
      }
    }

    return {
      branch: branchOutput || 'HEAD',
      ahead,
      behind,
      staged,
      unstaged,
      untracked,
      conflicted,
    };

  } catch (error) {
    logError('Failed to get git status', toError(error));
    throw new Error(`Git status failed: ${getErrorMessage(error)}`);
  }
}

/**
 * Get detailed diff result with file information
 */
export async function getDetailedDiff(
  workingDirectory = process.cwd(),
  options: GitDiffOptions = {},
): Promise<DiffResult> {
  try {
    // Get diff with stat information
    const statDiff = await getGitDiff(workingDirectory, { ...options, stat: true });
    const fullDiff = await getGitDiff(workingDirectory, options);
    
    // Parse stat information
    const files: DiffResult['files'] = [];
    let totalAdditions = 0;
    let totalDeletions = 0;
    
    const statLines = statDiff.split('\n');
    for (const line of statLines) {
      const match = line.match(/^\s*(.+?)\s+\|\s+(\d+)\s+([+-]+)$/);
      if (match) {
        const [, path, changesStr, symbols] = match;
        const changes = parseInt(changesStr);
        const additions = (symbols.match(/\+/g) || []).length;
        const deletions = (symbols.match(/-/g) || []).length;
        
        files.push({
          path: path.trim(),
          status: 'modified',
          additions,
          deletions,
          diff: extractFileDiff(fullDiff, path.trim()),
        });
        
        totalAdditions += additions;
        totalDeletions += deletions;
      }
    }
    
    return {
      files,
      summary: {
        totalFiles: files.length,
        totalAdditions,
        totalDeletions,
      },
    };

  } catch (error) {
    logError('Failed to get detailed diff', toError(error));
    throw error;
  }
}

/**
 * Extract diff for specific file from full diff output
 */
function extractFileDiff(fullDiff: string, filePath: string): string {
  const lines = fullDiff.split('\n');
  const result: string[] = [];
  let inFile = false;
  
  for (const line of lines) {
    if (line.startsWith('diff --git')) {
      inFile = line.includes(filePath);
      if (inFile) {
        result.push(line);
      }
    } else if (inFile) {
      if (line.startsWith('diff --git')) {
        break; // Next file started
      }
      result.push(line);
    }
  }
  
  return result.join('\n');
}

/**
 * Check if there are uncommitted changes
 */
export async function hasUncommittedChanges(workingDirectory = process.cwd()): Promise<boolean> {
  try {
    const status = await getGitStatus(workingDirectory);
    return status.staged.length > 0 || status.unstaged.length > 0;
  } catch (error) {
    return false;
  }
}

/**
 * Get current git branch
 */
export async function getCurrentBranch(workingDirectory = process.cwd()): Promise<string> {
  try {
    if (!checkInGit(workingDirectory)) {
      throw new Error('Not in a Git repository');
    }

    const output = execSync('git branch --show-current', {
      cwd: workingDirectory,
      encoding: 'utf-8',
    });

    return output.trim() || 'HEAD';
  } catch (error) {
    logError('Failed to get current branch', toError(error));
    return 'unknown';
  }
}

/**
 * Get git repository root
 */
export async function getGitRoot(workingDirectory = process.cwd()): Promise<string> {
  try {
    const output = execSync('git rev-parse --show-toplevel', {
      cwd: workingDirectory,
      encoding: 'utf-8',
    });

    return output.trim();
  } catch (error) {
    logError('Failed to get git root', toError(error));
    throw new Error('Not in a Git repository');
  }
}

/**
 * Format diff for display
 */
export function formatDiffForDisplay(diff: string): string {
  const lines = diff.split('\n');
  const formatted: string[] = [];
  
  for (const line of lines) {
    if (line.startsWith('+++') || line.startsWith('---')) {
      formatted.push(`{cyan-fg}${line}{/cyan-fg}`);
    } else if (line.startsWith('+')) {
      formatted.push(`{green-fg}${line}{/green-fg}`);
    } else if (line.startsWith('-')) {
      formatted.push(`{red-fg}${line}{/red-fg}`);
    } else if (line.startsWith('@@')) {
      formatted.push(`{yellow-fg}${line}{/yellow-fg}`);
    } else {
      formatted.push(line);
    }
  }
  
  return formatted.join('\n');
}

/**
 * Get short status summary
 */
export async function getShortStatus(workingDirectory = process.cwd()): Promise<string> {
  try {
    const status = await getGitStatus(workingDirectory);
    const parts: string[] = [];
    
    if (status.staged.length > 0) {
      parts.push(`${status.staged.length} staged`);
    }
    
    if (status.unstaged.length > 0) {
      parts.push(`${status.unstaged.length} unstaged`);
    }
    
    if (status.untracked.length > 0) {
      parts.push(`${status.untracked.length} untracked`);
    }
    
    if (status.conflicted.length > 0) {
      parts.push(`${status.conflicted.length} conflicted`);
    }
    
    if (status.ahead > 0) {
      parts.push(`${status.ahead} ahead`);
    }
    
    if (status.behind > 0) {
      parts.push(`${status.behind} behind`);
    }
    
    return parts.length > 0 ? parts.join(', ') : 'clean';
    
  } catch (error) {
    return 'unknown';
  }
}
